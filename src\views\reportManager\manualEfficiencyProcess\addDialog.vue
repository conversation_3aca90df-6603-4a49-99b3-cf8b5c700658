<template>
    <content-panel>  
  <qDialog :visible="visible" :innerScroll="false" :innerHeight="500" :title="title" width="500px"
    :isLoading="isLoading" @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <el-form :model="addForm" :rules="rules" ref="addForm" size="small" label-width="120px">
      <el-form-item label="工厂名称:" prop="factoryId">
        <el-select @change="addForm.smallGroupId = ''" v-model="addForm.factoryId" style="width: 300px" filterable
          placeholder="请选择工厂名称" clearable>
          <el-option v-for="item in tabList" :key="item.value" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="核算班组:" prop="smallGroupId">
        <el-select style="width: 300px" v-model="addForm.smallGroupId" filterable clearable placeholder="请选择核算班组">
          <el-option v-for="item in process.processList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
         <!-- <el-input
              v-model.trim="addForm.mesProcessNames"
              clearable
              disabled
              ype="text"
              placeholder="请选择小工序">
            <template
              slot="append" >
              <el-button @click="selectBigProcessCode">选择</el-button>
            </template>
          </el-input> -->
      </el-form-item>
      <el-form-item label="人工效率工序:" prop="name">
        <el-input show-word-limit maxlength="20" v-model="addForm.name" style="width: 300px" placeholder="请输入人工效率工序"
          clearable></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sortIndex">
        <el-input class="input-number" style="width: 300px" v-model="addForm.sortIndex" placeholder="请输入排序"
          type="number" :controls="false" oninput="value=value.replace(/[^\d]/g,'')" clearable></el-input>
      </el-form-item>
      <el-form-item label="组别" prop="groupLevel">
        <el-input show-word-limit maxlength="20" class="input-number" style="width: 300px" v-model="addForm.groupLevel"
          placeholder="请输入组别" :controls="false" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item label="启用状态:" prop="enable">
        <el-switch inactive-color="#ff4949" v-model="addForm.enable" :active-text="addForm.enable == '1' ? '启用' : '禁用'"
          :active-value="1" :inactive-value="0">
        </el-switch>
      </el-form-item> -->
      <el-form-item class="staffName" label="备注:" prop="remarks">
        <el-input style="width: 300px" type="textarea" resize="none" rows="5" maxlength="50" show-word-limit
          v-model.trim="addForm.remarks">
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
   <AddSmallGroupIds
      v-if="isSmallGroupvisible"
      :visible.sync="isSmallGroupvisible" 
      :checkData="smallGroupList"
      @confirmSmallGroupIds="confirmSmallGroup"
    ></AddSmallGroupIds>
    </content-panel>
</template>
<script>
import AddSmallGroupIds from "./addSmallGroupIds.vue";
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  components: { AddSmallGroupIds },
  data() {
    return {
      addForm: {
        factoryId: "",
        name: "", 
        smallGroupId: "",
        groupLevel: "",
        remarks: "",
        sortIndex: "",
      },
      tabList: [],
      smallGroupList:[],
      isSmallGroupvisible:false,
      showType: false,
      isLoading: false,
      rules: {
        factoryId: [
          { required: true, message: "请选择工厂", trigger: "change" },
        ],
        smallGroupId: [
          { required: true, message: "请选择核算班组", trigger: "change" },
        ],
        name: [
          { required: true, message: "请输入人工效率工序", trigger: "blur" },
        ],
        sortIndex: [
          { required: true, message: "请输入排序", trigger: "blur" },
        ]
      },
    };
  },
  async created() {
    await this.getBigGroupList();
    this.addForm = {
      ...this.addForm,
      ...this.editForm,
    };
    console.log(this.addForm, '数据');
  },
  computed: {
    process() {
      return {
        processList:
          (this.addForm.factoryId &&
            this.tabList.find((item) => item.id == this.addForm.factoryId)
              .process) ||
          [],
      };
    },
  },
  methods: {
    //确认核算班组
    confirmSmallGroup(){

    },
    getBigGroupList() {
      return this.$api.systemManage.getBasicPermission
        .getBasicPermissionAll1()
        .then(({ data }) => {
          this.tabList = data || [];
        });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
          parentId: this.addForm.factoryId,
          ...this.addForm,
          name: this.addForm.name.trim(),
          groupLevel: this.addForm.groupLevel.trim()
        };
        if (this.title == "新增") {
          this.$api.reportManagement
            .addLaborEfficiency(params)
            .then(({ success }) => {
              if (success)
                this.$notify({
                  title: "成功",
                  message: "新增成功",
                  type: "success",
                });
              this.$emit("cancel", "confirm");
            })
            .finally(() => {
              this.isLoading = false;
            });
        } else {
          this.$api.reportManagement
            .updateLaborEfficiency(params)
            .then(({ success }) => {
              if (success)
                this.$notify({
                  title: "成功",
                  message: "修改成功",
                  type: "success",
                });
              this.$emit("cancel", "confirm");
            })
            .finally(() => {
              this.isLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>

</style>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
